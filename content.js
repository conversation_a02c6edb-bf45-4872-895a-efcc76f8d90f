class FacebookPageScraper {
  constructor() {
    this.scrapedPages = [];
    this.maxPages = 5;
    this.currentPage = 0;
    this.isScrolling = false;
    this.scrollDelay = 2000; // 2 seconds between scrolls
  }

  async startScraping() {
    console.log('Starting Facebook page scraping...');
    this.sendProgress(5, 'Initializing scraper...');

    try {
      // Initial scrape of visible content
      console.log('Starting initial scrape...');
      this.sendProgress(10, 'Scanning visible pages...');
      this.scrapeVisiblePages();
      console.log(`Found ${this.scrapedPages.length} pages so far`);
      this.sendProgress(20, `Found ${this.scrapedPages.length} pages - scrolling for more...`);

      // Scroll and scrape additional pages
      for (let i = 1; i < this.maxPages; i++) {
        console.log(`Scrolling to load page ${i + 1}...`);
        this.sendProgress(20 + (i * 15), `Loading page ${i + 1} of ${this.maxPages}...`);

        await this.scrollAndWait();

        const beforeCount = this.scrapedPages.length;
        this.scrapeVisiblePages();
        const afterCount = this.scrapedPages.length;

        console.log(`After scroll ${i}: found ${afterCount - beforeCount} new pages`);
        this.currentPage = i + 1;
        this.sendProgress(20 + (i * 15) + 10,
                         `Page ${this.currentPage}: ${afterCount} total pages found`);
      }

      // Remove duplicates and send final results
      console.log('Processing final results...');
      this.sendProgress(95, 'Processing results...');
      const uniquePages = this.removeDuplicates(this.scrapedPages);
      console.log(`Final result: ${uniquePages.length} unique pages`);
      this.sendComplete(uniquePages);

    } catch (error) {
      console.error('Scraping error:', error);
      this.sendError(error.message);
    }
  }

  scrapeVisiblePages() {
    // Facebook page search results - try multiple selectors
    const selectors = [
      '[role="article"]',
      '[data-pagelet*="page"]',
      '[data-testid*="result"]',
      '.x1yztbdb', // Common Facebook container class
      'div[style*="border-radius"]' // Page cards often have border radius
    ];

    let pageElements = [];
    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        pageElements = Array.from(elements);
        console.log(`Found ${elements.length} elements with selector: ${selector}`);
        break;
      }
    }

    if (pageElements.length === 0) {
      console.log('No page elements found, trying broader search...');
      // Fallback: look for any div that contains typical page info
      pageElements = Array.from(document.querySelectorAll('div')).filter(div => {
        const text = div.textContent;
        return text.includes('followers') || text.includes('recommend') || text.includes('·');
      });
    }

    console.log(`Processing ${pageElements.length} potential page elements`);

    pageElements.forEach((element, index) => {
      try {
        const pageData = this.extractPageData(element);
        if (pageData && pageData.name && pageData.name.length > 1) {
          console.log(`Extracted page data ${index + 1}:`, pageData);
          this.scrapedPages.push(pageData);
        }
      } catch (error) {
        console.warn('Error extracting page data:', error);
      }
    });
  }

  extractPageData(element) {
    const pageData = {
      name: '',
      category: '',
      recommendation: '',
      location: '',
      status: '',
      followers: '',
      description: ''
    };

    // Skip if element is too small or doesn't contain relevant content
    const text = element.textContent.trim();
    if (text.length < 20 || (!text.includes('followers') && !text.includes('recommend') && !text.includes('·'))) {
      return null;
    }

    // Extract page name - try multiple approaches
    let nameElement = element.querySelector('h1, h2, h3, h4, strong, [role="heading"]');
    if (!nameElement) {
      // Look for the first significant text that's not metadata
      const textNodes = this.getTextNodes(element);
      for (const node of textNodes) {
        const nodeText = node.textContent.trim();
        if (nodeText.length > 2 && nodeText.length < 100 &&
            !nodeText.includes('·') && !nodeText.includes('%') &&
            !nodeText.includes('followers') && !nodeText.includes('Follow')) {
          pageData.name = nodeText;
          break;
        }
      }
    } else {
      pageData.name = nameElement.textContent.trim();
    }

    // Parse the full text for metadata
    const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);

    for (const line of lines) {
      // Look for the info line with bullet separators
      if (line.includes('·')) {
        const parts = line.split('·').map(part => part.trim());

        parts.forEach(part => {
          if (part.includes('%') && (part.includes('recommend') || part.includes('rating'))) {
            pageData.recommendation = part;
          } else if (part.includes('followers') || part.includes('likes') || /\d+[KM]?\s*(followers|likes)/i.test(part)) {
            pageData.followers = part;
          } else if (part.includes('Always open') || part.includes('Closed') || part.includes('Open') || part.includes('Hours')) {
            pageData.status = part;
          } else if (this.looksLikeLocation(part)) {
            pageData.location = part;
          } else if (!pageData.category && part.length > 0 && part.length < 50 &&
                    !part.includes('Follow') && !part.includes('Like') && !part.includes('Message')) {
            pageData.category = part;
          }
        });
      }

      // Look for description (longer text lines)
      if (line.length > 50 && line.length < 300 && !line.includes('·') &&
          !pageData.description && !line.includes('Follow') && !line.includes('Message')) {
        pageData.description = line;
      }
    }

    return pageData.name ? pageData : null;
  }

  findInfoLine(element) {
    // Look for text that contains typical page info patterns
    const textNodes = this.getTextNodes(element);
    
    for (const node of textNodes) {
      const text = node.textContent.trim();
      if (text.includes('·') && (text.includes('%') || text.includes('followers') || text.includes('recommend'))) {
        return text;
      }
    }
    
    return element.textContent;
  }

  getTextNodes(element) {
    const textNodes = [];
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );
    
    let node;
    while (node = walker.nextNode()) {
      if (node.textContent.trim().length > 0) {
        textNodes.push(node);
      }
    }
    
    return textNodes;
  }

  looksLikeLocation(text) {
    // Simple heuristic to identify location strings
    const locationPatterns = [
      /\w+,\s*\w{2}/, // City, State
      /\w+,\s*\w+/, // City, Country
      /\d+\s+\w+/, // Street address pattern
    ];
    
    return locationPatterns.some(pattern => pattern.test(text));
  }

  async scrollAndWait() {
    return new Promise((resolve) => {
      // Scroll to bottom to trigger infinite scroll
      window.scrollTo(0, document.body.scrollHeight);
      
      // Wait for content to load
      setTimeout(resolve, this.scrollDelay);
    });
  }

  removeDuplicates(pages) {
    const seen = new Set();
    return pages.filter(page => {
      const key = page.name + page.category;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  sendProgress(progress, status) {
    chrome.runtime.sendMessage({
      type: 'SCRAPE_PROGRESS',
      progress: progress,
      status: status
    });
  }

  sendComplete(data) {
    chrome.runtime.sendMessage({
      type: 'SCRAPE_COMPLETE',
      data: data
    });
  }

  sendError(error) {
    chrome.runtime.sendMessage({
      type: 'SCRAPE_ERROR',
      error: error
    });
  }
}

// Initialize scraper
const scraper = new FacebookPageScraper();

// Listen for messages from popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Content script received message:', message);

  if (message.type === 'START_SCRAPING') {
    console.log('Starting scraping process...');
    scraper.startScraping();
    sendResponse({ success: true });
  }

  return true; // Keep message channel open for async response
});
