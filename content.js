class FacebookPageScraper {
  constructor() {
    this.scrapedPages = [];
    this.maxPages = 5;
    this.currentPage = 0;
    this.isScrolling = false;
    this.scrollDelay = 2000; // 2 seconds between scrolls
  }

  async startScraping() {
    console.log('Starting Facebook page scraping...');
    
    try {
      // Initial scrape of visible content
      this.scrapeVisiblePages();
      this.sendProgress(20, `Scraped page 1 of ${this.maxPages}`);
      
      // Scroll and scrape additional pages
      for (let i = 1; i < this.maxPages; i++) {
        await this.scrollAndWait();
        this.scrapeVisiblePages();
        this.currentPage = i + 1;
        this.sendProgress((this.currentPage / this.maxPages) * 100, 
                         `Scraped page ${this.currentPage} of ${this.maxPages}`);
      }
      
      // Remove duplicates and send final results
      const uniquePages = this.removeDuplicates(this.scrapedPages);
      this.sendComplete(uniquePages);
      
    } catch (error) {
      console.error('Scraping error:', error);
      this.sendError(error.message);
    }
  }

  scrapeVisiblePages() {
    // Facebook page search results are typically in div elements with specific patterns
    const pageElements = document.querySelectorAll('[role="article"], [data-pagelet*="page"]');
    
    pageElements.forEach(element => {
      try {
        const pageData = this.extractPageData(element);
        if (pageData && pageData.name) {
          this.scrapedPages.push(pageData);
        }
      } catch (error) {
        console.warn('Error extracting page data:', error);
      }
    });
  }

  extractPageData(element) {
    const pageData = {
      name: '',
      category: '',
      recommendation: '',
      location: '',
      status: '',
      followers: '',
      description: ''
    };

    // Extract page name (usually in a heading or strong text)
    const nameElement = element.querySelector('h3, h4, strong, [role="heading"]');
    if (nameElement) {
      pageData.name = nameElement.textContent.trim();
    }

    // Extract all text content to parse different fields
    const textContent = element.textContent;
    
    // Extract category, recommendation, location, status (these are usually in a single line)
    const infoLine = this.findInfoLine(element);
    if (infoLine) {
      const parts = infoLine.split('·').map(part => part.trim());
      
      parts.forEach(part => {
        if (part.includes('%') && part.includes('recommend')) {
          pageData.recommendation = part;
        } else if (part.includes('followers') || part.includes('likes')) {
          pageData.followers = part;
        } else if (part.includes('Always open') || part.includes('Closed') || part.includes('Open')) {
          pageData.status = part;
        } else if (this.looksLikeLocation(part)) {
          pageData.location = part;
        } else if (!pageData.category && part.length > 0 && !part.includes('Follow')) {
          pageData.category = part;
        }
      });
    }

    // Extract description (usually longer text)
    const descElement = element.querySelector('span[dir="auto"]');
    if (descElement && descElement.textContent.length > 50) {
      pageData.description = descElement.textContent.trim();
    }

    return pageData;
  }

  findInfoLine(element) {
    // Look for text that contains typical page info patterns
    const textNodes = this.getTextNodes(element);
    
    for (const node of textNodes) {
      const text = node.textContent.trim();
      if (text.includes('·') && (text.includes('%') || text.includes('followers') || text.includes('recommend'))) {
        return text;
      }
    }
    
    return element.textContent;
  }

  getTextNodes(element) {
    const textNodes = [];
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );
    
    let node;
    while (node = walker.nextNode()) {
      if (node.textContent.trim().length > 0) {
        textNodes.push(node);
      }
    }
    
    return textNodes;
  }

  looksLikeLocation(text) {
    // Simple heuristic to identify location strings
    const locationPatterns = [
      /\w+,\s*\w{2}/, // City, State
      /\w+,\s*\w+/, // City, Country
      /\d+\s+\w+/, // Street address pattern
    ];
    
    return locationPatterns.some(pattern => pattern.test(text));
  }

  async scrollAndWait() {
    return new Promise((resolve) => {
      // Scroll to bottom to trigger infinite scroll
      window.scrollTo(0, document.body.scrollHeight);
      
      // Wait for content to load
      setTimeout(resolve, this.scrollDelay);
    });
  }

  removeDuplicates(pages) {
    const seen = new Set();
    return pages.filter(page => {
      const key = page.name + page.category;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  sendProgress(progress, status) {
    chrome.runtime.sendMessage({
      type: 'SCRAPE_PROGRESS',
      progress: progress,
      status: status
    });
  }

  sendComplete(data) {
    chrome.runtime.sendMessage({
      type: 'SCRAPE_COMPLETE',
      data: data
    });
  }

  sendError(error) {
    chrome.runtime.sendMessage({
      type: 'SCRAPE_ERROR',
      error: error
    });
  }
}

// Initialize scraper
const scraper = new FacebookPageScraper();

// Listen for messages from popup
window.addEventListener('message', function(event) {
  if (event.data.type === 'START_SCRAPING') {
    scraper.startScraping();
  }
});

// Also listen for direct calls from popup script injection
if (typeof startScraping !== 'undefined') {
  scraper.startScraping();
}
