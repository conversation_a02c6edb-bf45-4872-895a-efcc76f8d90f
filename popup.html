<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 20px;
      font-family: Arial, sans-serif;
    }
    
    .container {
      text-align: center;
    }
    
    h1 {
      font-size: 18px;
      margin-bottom: 20px;
      color: #1877f2;
    }
    
    button {
      background-color: #1877f2;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      margin: 5px;
      width: 100%;
    }
    
    button:hover {
      background-color: #166fe5;
    }
    
    button:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }
    
    .progress {
      margin: 15px 0;
      display: none;
    }
    
    .progress-bar {
      width: 100%;
      height: 20px;
      background-color: #f0f0f0;
      border-radius: 10px;
      overflow: hidden;
    }
    
    .progress-fill {
      height: 100%;
      background-color: #1877f2;
      width: 0%;
      transition: width 0.3s ease;
    }
    
    .status {
      margin: 10px 0;
      font-size: 12px;
      color: #666;
    }
    
    .results {
      margin-top: 15px;
      font-size: 12px;
      color: #333;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Facebook Page Scraper</h1>
    
    <button id="scrapeBtn">Start Scraping</button>
    
    <div class="progress" id="progressContainer">
      <div class="progress-bar">
        <div class="progress-fill" id="progressFill"></div>
      </div>
      <div class="status" id="statusText">Initializing...</div>
    </div>
    
    <div class="results" id="results"></div>
    
    <button id="downloadBtn" style="display: none;">Download CSV</button>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
