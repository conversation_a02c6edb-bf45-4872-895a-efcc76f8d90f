let scrapedData = [];

document.addEventListener('DOMContentLoaded', function() {
  const scrapeBtn = document.getElementById('scrapeBtn');
  const downloadBtn = document.getElementById('downloadBtn');
  const progressContainer = document.getElementById('progressContainer');
  const progressFill = document.getElementById('progressFill');
  const statusText = document.getElementById('statusText');
  const results = document.getElementById('results');

  scrapeBtn.addEventListener('click', async function() {
    // Check if we're on a Facebook search page
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    
    if (!tab.url.includes('facebook.com/search/pages/')) {
      alert('Please navigate to a Facebook page search results page first!');
      return;
    }

    // Start scraping
    scrapeBtn.disabled = true;
    scrapeBtn.textContent = 'Scraping...';
    progressContainer.style.display = 'block';
    
    try {
      // Inject and execute the scraping script
      await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        function: startScraping
      });
      
      // Listen for messages from content script
      chrome.runtime.onMessage.addListener(handleMessage);
      
    } catch (error) {
      console.error('Error starting scrape:', error);
      resetUI();
    }
  });

  downloadBtn.addEventListener('click', function() {
    downloadCSV();
  });

  function handleMessage(message, sender, sendResponse) {
    if (message.type === 'SCRAPE_PROGRESS') {
      updateProgress(message.progress, message.status);
    } else if (message.type === 'SCRAPE_COMPLETE') {
      scrapedData = message.data;
      onScrapeComplete();
    } else if (message.type === 'SCRAPE_ERROR') {
      console.error('Scraping error:', message.error);
      resetUI();
    }
  }

  function updateProgress(progress, status) {
    progressFill.style.width = progress + '%';
    statusText.textContent = status;
  }

  function onScrapeComplete() {
    progressFill.style.width = '100%';
    statusText.textContent = 'Scraping complete!';
    results.textContent = `Found ${scrapedData.length} pages`;
    
    scrapeBtn.style.display = 'none';
    downloadBtn.style.display = 'block';
  }

  function resetUI() {
    scrapeBtn.disabled = false;
    scrapeBtn.textContent = 'Start Scraping';
    progressContainer.style.display = 'none';
    progressFill.style.width = '0%';
  }

  function downloadCSV() {
    if (scrapedData.length === 0) {
      alert('No data to download!');
      return;
    }

    const csvContent = convertToCSV(scrapedData);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `facebook_pages_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }

  function convertToCSV(data) {
    const headers = ['Page Name', 'Category', 'Recommendation', 'Location', 'Status', 'Followers', 'Description'];
    const csvRows = [headers.join(',')];
    
    data.forEach(page => {
      const row = [
        escapeCSV(page.name),
        escapeCSV(page.category),
        escapeCSV(page.recommendation),
        escapeCSV(page.location),
        escapeCSV(page.status),
        escapeCSV(page.followers),
        escapeCSV(page.description)
      ];
      csvRows.push(row.join(','));
    });
    
    return csvRows.join('\n');
  }

  function escapeCSV(field) {
    if (field === null || field === undefined) return '';
    const str = String(field);
    if (str.includes(',') || str.includes('"') || str.includes('\n')) {
      return '"' + str.replace(/"/g, '""') + '"';
    }
    return str;
  }
});

// This function will be injected into the page
function startScraping() {
  // This will be handled by content.js
  window.postMessage({ type: 'START_SCRAPING' }, '*');
}
