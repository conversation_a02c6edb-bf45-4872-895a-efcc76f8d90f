let scrapedData = [];

document.addEventListener('DOMContentLoaded', function() {
  const scrapeBtn = document.getElementById('scrapeBtn');
  const downloadBtn = document.getElementById('downloadBtn');
  const progressContainer = document.getElementById('progressContainer');
  const progressFill = document.getElementById('progressFill');
  const statusText = document.getElementById('statusText');
  const results = document.getElementById('results');

  scrapeBtn.addEventListener('click', async function() {
    // Check if we're on a Facebook search page
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

    if (!tab.url.includes('facebook.com/search/pages/')) {
      alert('Please navigate to a Facebook page search results page first!');
      return;
    }

    // Start scraping
    scrapeBtn.disabled = true;
    scrapeBtn.textContent = 'Scraping...';
    progressContainer.style.display = 'block';
    statusText.textContent = 'Starting scraper...';

    // Set up message listener first
    chrome.runtime.onMessage.addListener(handleMessage);

    try {
      // Try to send message to content script
      try {
        await chrome.tabs.sendMessage(tab.id, { type: 'START_SCRAPING' });
      } catch (messageError) {
        console.log('Content script not ready, injecting...');
        // If content script isn't loaded, inject it
        await chrome.scripting.executeScript({
          target: { tabId: tab.id },
          files: ['content.js']
        });

        // Wait a moment for script to initialize
        await new Promise(resolve => setTimeout(resolve, 500));

        // Try sending message again
        await chrome.tabs.sendMessage(tab.id, { type: 'START_SCRAPING' });
      }

    } catch (error) {
      console.error('Error starting scrape:', error);
      statusText.textContent = 'Error: ' + error.message;
      resetUI();
    }
  });

  downloadBtn.addEventListener('click', function() {
    downloadCSV();
  });

  function handleMessage(message, sender, sendResponse) {
    if (message.type === 'SCRAPE_PROGRESS') {
      updateProgress(message.progress, message.status);
    } else if (message.type === 'SCRAPE_COMPLETE') {
      scrapedData = message.data;
      onScrapeComplete();
    } else if (message.type === 'SCRAPE_ERROR') {
      console.error('Scraping error:', message.error);
      resetUI();
    }
  }

  function updateProgress(progress, status) {
    progressFill.style.width = progress + '%';
    statusText.textContent = status;
  }

  function onScrapeComplete() {
    progressFill.style.width = '100%';
    statusText.textContent = 'Scraping complete!';
    results.textContent = `Found ${scrapedData.length} pages`;
    
    scrapeBtn.style.display = 'none';
    downloadBtn.style.display = 'block';
  }

  function resetUI() {
    scrapeBtn.disabled = false;
    scrapeBtn.textContent = 'Start Scraping';
    progressContainer.style.display = 'none';
    progressFill.style.width = '0%';
  }

  function downloadCSV() {
    if (scrapedData.length === 0) {
      alert('No data to download!');
      return;
    }

    const csvContent = convertToCSV(scrapedData);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `facebook_pages_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }

  function convertToCSV(data) {
    const headers = ['Page Name', 'Category', 'Recommendation', 'Location', 'Status', 'Followers', 'Description'];
    const csvRows = [headers.join(',')];
    
    data.forEach(page => {
      const row = [
        escapeCSV(page.name),
        escapeCSV(page.category),
        escapeCSV(page.recommendation),
        escapeCSV(page.location),
        escapeCSV(page.status),
        escapeCSV(page.followers),
        escapeCSV(page.description)
      ];
      csvRows.push(row.join(','));
    });
    
    return csvRows.join('\n');
  }

  function escapeCSV(field) {
    if (field === null || field === undefined) return '';
    const str = String(field);
    if (str.includes(',') || str.includes('"') || str.includes('\n')) {
      return '"' + str.replace(/"/g, '""') + '"';
    }
    return str;
  }
});


