# Facebook Page Scraper Chrome Extension

A Chrome extension that scrapes Facebook page information from search results pages and exports the data as CSV.

## Features

- Scrapes up to 5 pages of Facebook search results
- Extracts page name, category, recommendations, location, status, followers, and description
- Shows progress during scraping
- Exports data as CSV file
- Works with logged-in Facebook accounts

## Installation

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" in the top right corner
3. Click "Load unpacked" and select the folder containing these extension files
4. The extension icon should appear in your Chrome toolbar

## Usage

1. Navigate to a Facebook page search results page (e.g., `https://www.facebook.com/search/pages/?q=dogs`)
2. Make sure you're logged into Facebook
3. Click the extension icon in your Chrome toolbar
4. Click "Start Scraping" in the popup
5. Wait for the scraping to complete (progress will be shown)
6. Click "Download CSV" to save the results

## Data Fields Extracted

- **Page Name**: The name of the Facebook page
- **Category**: The page category (e.g., "Dog Trainer")
- **Recommendation**: Recommendation percentage (e.g., "92% recommend")
- **Location**: Page location (e.g., "New York, NY")
- **Status**: Operating status (e.g., "Always open")
- **Followers**: Number of followers/likes
- **Description**: Page description text

## Technical Details

- Uses Chrome Extension Manifest V3
- Implements infinite scroll handling with 2-second delays between scrolls
- Removes duplicate entries automatically
- Exports data in CSV format with proper escaping

## Files Structure

- `manifest.json` - Extension configuration
- `popup.html` - Extension popup interface
- `popup.js` - Popup logic and CSV export functionality
- `content.js` - Content script for scraping Facebook pages

## Notes

- The extension is designed to work with the current Facebook layout (as of 2024)
- Facebook may change their page structure, which could require updates to the selectors
- Scraping is limited to 5 pages to avoid potential rate limiting
- Works best with logged-in Facebook accounts for full access to search results

## Disclaimer

This extension is for educational purposes. Please respect Facebook's Terms of Service and use responsibly.
